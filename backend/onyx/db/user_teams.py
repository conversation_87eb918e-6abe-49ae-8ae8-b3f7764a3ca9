from collections.abc import Sequence
from operator import and_
from uuid import UUI<PERSON>

from fastapi import <PERSON><PERSON>P<PERSON>xception
from sqlalchemy import delete
from sqlalchemy import func
from sqlalchemy import Select
from sqlalchemy import select
from sqlalchemy import update
from sqlalchemy.orm import Session

from onyx.server.user_teams.models import CreateUserTeams
from onyx.server.user_teams.models import UpdateUserTeams
from onyx.db.models import DocumentSet__UserGroup
from onyx.db.models import LLMProvider__UserGroup
from onyx.db.models import Persona__UserGroup
from onyx.db.models import TokenRateLimit__UserGroup
from onyx.db.models import User
from onyx.db.models import User__UserGroup
from onyx.db.models import UserGroup
from onyx.db.models import UserGroup__ConnectorCredentialPair
from onyx.utils.logger import setup_logger
from onyx.db.models import InvitedUser
from onyx.auth.schemas import UserRole

logger = setup_logger()


def check_user_belongs_to_team(db_session: Session, user_id: UUID) -> bool:
    """
    Check if a user belongs to any active team.
    Returns True if the user is assigned to at least one team, False otherwise.
    """
    user_team_relationship = db_session.query(User__UserGroup).filter(
        User__UserGroup.user_id == user_id
    ).join(UserGroup).filter(
        UserGroup.is_up_to_date == True  # noqa: E712
    ).first()

    return user_team_relationship is not None


def get_user_teams_for_user(db_session: Session, user_id: UUID) -> list[UserGroup]:
    """
    Get all active teams that a user belongs to.
    """
    return db_session.query(UserGroup).join(User__UserGroup).filter(
        User__UserGroup.user_id == user_id,
        UserGroup.is_up_to_date == True  # noqa: E712
    ).all()


def remove_user_from_all_teams(db_session: Session, user_id: UUID) -> None:
    """
    Remove a user from all teams. Used when promoting to admin.
    """
    user_team_relationships = db_session.query(User__UserGroup).filter(
        User__UserGroup.user_id == user_id
    ).all()

    for relationship in user_team_relationships:
        db_session.delete(relationship)


def update_user_invitation_status_based_on_role_and_team(
    db_session: Session,
    user_id: UUID,
    new_role: UserRole
) -> None:
    """
    Update the invitation status of a user based on their role and team assignment.
    NEW LOGIC:
    - Admins: always ready_to_signup (and should NOT be in teams)
    - Non-admins with team assignment: ready_to_signup
    - Non-admins without team assignment: pending_assignment
    """
    # Check if user has an invitation record
    invite = db_session.query(InvitedUser).filter_by(id=user_id).first()
    if not invite:
        return

    if new_role == UserRole.ADMIN:
        # Admins can always signup and should not be in teams
        invite.status = "ready_to_signup"
    else:
        # Non-admins (basic, team_admin) MUST have team assignment
        if check_user_belongs_to_team(db_session, user_id):
            invite.status = "ready_to_signup"
        else:
            invite.status = "pending_assignment"

    db_session.add(invite)


def create_user_teams(db_session: Session, user_teams: CreateUserTeams) -> UserGroup:
    # Check if a user_team with the same name already exists
    existing_user_teams = db_session.scalar(
        select(UserGroup).where(UserGroup.name == user_teams.name)
    )
    if existing_user_teams is not None:
        raise ValueError("User team with this name already exist")

    # Validate all user_ids and check that no admins are being added to teams
    if user_teams.user_ids:
        users = db_session.execute(
            select(User.id, User.role).where(User.id.in_(user_teams.user_ids))
        ).all()

        valid_user_ids = set(user_id for user_id, role in users)
        admin_users = [user_id for user_id, role in users if role == UserRole.ADMIN]

        if admin_users:
            raise ValueError(f"Admin users cannot be added to teams. Admin user IDs: {admin_users}")

        for user_id in user_teams.user_ids:
            if user_id not in valid_user_ids:
                raise ValueError(f"User_id {user_id} is not valid")
    else:
        raise ValueError("User team must have at least one user.")

    db_user_team = UserGroup(
        name=user_teams.name, is_up_to_date=True, time_last_modified_by_user=func.now()
    )
    db_session.add(db_user_team)
    db_session.flush()  # give the user_team an ID

    relationships = []
    for user_id in user_teams.user_ids:
        user = db_session.query(User).filter(User.id == user_id).first()
        if user:
            relationships.append(User__UserGroup(user_id=user_id, user_group_id=db_user_team.id))
        else:
            # Try to find a pending invited user with this email
            invited_users = db_session.query(InvitedUser).filter_by(status="pending_assignment").all()
            invite = next((i for i in invited_users if i.id == user_id), None)
            if invite:
                relationships.append(User__UserGroup(user_id=invite.id, user_group_id=db_user_team.id))
    db_session.add_all(relationships)

    db_session.commit()

    # --- Update invited user status in DB if needed ---
    updated = False
    for user_id in user_teams.user_ids:
        db_invite = db_session.query(InvitedUser).filter_by(id=user_id, status="pending_assignment").first()
        if db_invite:
            db_invite.status = "ready_to_signup"
            updated = True
    if updated:
        db_session.commit()
    # --- End update ---

    return db_user_team


def update_user_teams(
    db_session: Session,
    user: User | None,
    user_team_id: int,
    user_team_update: UpdateUserTeams,
) -> UserGroup:
    stmt = select(UserGroup).where(UserGroup.id == user_team_id)
    db_user_team = db_session.scalar(stmt)
    if db_user_team is None:
        raise ValueError(f"User team with id '{user_team_id}' not found")

    if not db_user_team.is_up_to_date:
        raise ValueError("User team does not exist!")

    # Update user_team name if different
    if user_team_update.name != db_user_team.name:
        # Check if another user_team with the same name already exists
        existing_user_team = db_session.scalar(
            select(UserGroup).where(UserGroup.name == user_team_update.name, UserGroup.id != user_team_id)
        )
        if existing_user_team is not None:
            raise ValueError("User team with this name already exist")
        db_user_team.name = user_team_update.name

    # Validate all user_ids in update and check that no admins are being added to teams
    if user_team_update.user_ids:
        users = db_session.execute(
            select(User.id, User.role).where(User.id.in_(user_team_update.user_ids))
        ).all()

        valid_user_ids = set(user_id for user_id, role in users)
        admin_users = [user_id for user_id, role in users if role == UserRole.ADMIN]

        if admin_users:
            raise ValueError(f"Admin users cannot be added to teams. Admin user IDs: {admin_users}")

        for user_id in user_team_update.user_ids:
            if user_id not in valid_user_ids:
                raise ValueError(f"User_id {user_id} is not valid")

    current_user_ids = set([user.id for user in db_user_team.users])
    updated_user_ids = set(user_team_update.user_ids)
    added_user_ids = list(updated_user_ids - current_user_ids)
    removed_user_ids = list(current_user_ids - updated_user_ids)

    # if (removed_user_ids or added_user_ids) and (
    #     not user or user.role != UserRole.ADMIN
    # ):
    #     raise ValueError("Only admins can add or remove users from user user_team")

    if removed_user_ids:
        where_clause = User__UserGroup.user_id.in_(removed_user_ids)
        user__user_group_relationships = db_session.scalars(
            select(User__UserGroup).where(where_clause)
        ).all()
        for user__user_group_relationship in user__user_group_relationships:
            db_session.delete(user__user_group_relationship)

    if added_user_ids:
        relationships = []
        for user_id in added_user_ids:
            user = db_session.query(User).filter(User.id == user_id).first()
            if user:
                relationships.append(User__UserGroup(user_id=user_id, user_group_id=user_team_id))
            else:
                invited_users = db_session.query(InvitedUser).filter_by(status="pending_assignment").all()
                invite = next((i for i in invited_users if i.id == user_id), None)
                if invite:
                    relationships.append(User__UserGroup(user_id=invite.id, user_group_id=user_team_id))
        db_session.add_all(relationships)

    # Mark user_team as modified
    db_user_team.time_last_modified_by_user = func.now()

    db_session.commit()

    # --- Update invited user status in DB for added users ---
    updated = False
    for user_id in added_user_ids:
        db_invite = db_session.query(InvitedUser).filter_by(id=user_id, status="pending_assignment").first()
        if db_invite:
            db_invite.status = "ready_to_signup"
            updated = True

    # --- Update status for removed users (check if they still belong to other teams) ---
    for user_id in removed_user_ids:
        # Get the user to check their role
        user = db_session.query(User).filter_by(id=user_id).first()
        if user and user.role != UserRole.ADMIN:
            # Non-admin users must have team assignment
            # Update their invitation status based on role and remaining team assignments
            update_user_invitation_status_based_on_role_and_team(
                db_session, user_id, user.role
            )
            updated = True

            # Log the status change for removed users
            has_remaining_teams = check_user_belongs_to_team(db_session, user_id)
            if not has_remaining_teams:
                logger.warning(f"User {user.email} ({user.role.value}) removed from team and has no remaining teams. Status set to pending_assignment.")
                logger.info(f"Active sessions for user {user.email} will be invalidated on next request due to team removal.")

    if updated:
        db_session.commit()
    # --- End update ---

    return db_user_team



def get_user_teams(
    db_session: Session, only_up_to_date: bool = True
) -> Sequence[UserGroup]:
    """
    Gets user teams from the database.
    """
    stmt = select(UserGroup)
    if only_up_to_date:
        stmt = stmt.where(UserGroup.is_up_to_date == True)  # noqa: E712
    return db_session.scalars(stmt).all()

def prepare_user_teams_for_deletion(db_session: Session, user_team_id: int) -> None:
    stmt = select(UserGroup).where(UserGroup.id == user_team_id)
    db_user_team = db_session.scalar(stmt)
    if db_user_team is None:
        raise ValueError(f"User team with id '{user_team_id}' not found")

    if not db_user_team.is_up_for_deletion:

        #cleanup_user__user_group_relationships
        where_clause = User__UserGroup.user_group_id == user_team_id
        user__user_group_relationships = db_session.scalars(
            select(User__UserGroup).where(where_clause)
        ).all()
        for user__user_group_relationship in user__user_group_relationships:
            db_session.delete(user__user_group_relationship)

        # # cleanup_token_rate_limit__user_group_relationships
        # token_rate_limit__user_group_relationships = db_session.scalars(
        #     select(TokenRateLimit__UserGroup).where(
        #         TokenRateLimit__UserGroup.user_group_id == user_team_id
        #     )
        # ).all()
        # for (
        #     token_rate_limit__user_group_relationship
        # ) in token_rate_limit__user_group_relationships:
        #     db_session.delete(token_rate_limit__user_group_relationship)


        # # cleanup_document_set__user_group_relationships
        # db_session.execute(
        #     delete(DocumentSet__UserGroup).where(
        #         DocumentSet__UserGroup.user_group_id == user_team_id
        #     )
        # )

        # # cleanup_persona__user_group_relationships
        # db_session.query(Persona__UserGroup).filter(
        #     Persona__UserGroup.user_group_id == user_team_id
        # ).delete(synchronize_session=False)

        # # cleanup_user_group__cc_pair_relationships
        # stmt = select(UserGroup__ConnectorCredentialPair).where(
        #     UserGroup__ConnectorCredentialPair.user_group_id == user_team_id
        # )
        # stmt = stmt.where(
        #     UserGroup__ConnectorCredentialPair.is_current == False  # noqa: E712
        # )
        # user_group__cc_pair_relationships = db_session.scalars(stmt)
        # for user_group__cc_pair_relationship in user_group__cc_pair_relationships:
        #     db_session.delete(user_group__cc_pair_relationship)


        # cleanup_llm_provider__user_group_relationships
        db_session.query(LLMProvider__UserGroup).filter(
            LLMProvider__UserGroup.user_group_id == user_team_id
        ).delete(synchronize_session=False)

        db_user_team.is_up_to_date = False
        db_user_team.is_up_for_deletion = True
        db_session.commit()
    
    else:
        raise ValueError(f"User team with id '{user_team_id}' not found")