from typing import Generic
from typing import <PERSON><PERSON>
from typing import TypeVar
from uuid import <PERSON><PERSON><PERSON>

from pydantic import BaseModel

from onyx.auth.schemas import User<PERSON>ole
from onyx.db.models import User


DataT = TypeVar("DataT")


class StatusResponse(BaseModel, Generic[DataT]):
    success: bool
    message: Optional[str] = None
    data: Optional[DataT] = None


class ApiKey(BaseModel):
    api_key: str


class IdReturn(BaseModel):
    id: int


class MinimalUserSnapshot(BaseModel):
    id: UUID
    email: str

class UserIdSnapshot(BaseModel):
    id: UUID

class FullUserSnapshot(BaseModel):
    id: UUID
    email: str
    role: UserRole
    is_active: bool
    password_configured: bool

    @classmethod
    def from_user_model(cls, user: User) -> "FullUserSnapshot":
        return cls(
            id=user.id,
            email=user.email,
            role=user.role,
            is_active=user.is_active,
            password_configured=user.password_configured,
        )


class InvitedUserSnapshot(BaseModel):
    email: str
    role: User<PERSON>ole | None = None
    status: str | None = None
    user_id: str | None = None


class DisplayPriorityRequest(BaseModel):
    display_priority_map: dict[int, int]
