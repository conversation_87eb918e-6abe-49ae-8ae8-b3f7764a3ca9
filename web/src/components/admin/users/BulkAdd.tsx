"use client";

import { with<PERSON><PERSON><PERSON>, FormikProps, FormikErrors, Form, Field } from "formik";
import { Button } from "@/components/ui/button";
import { UserRole, USER_ROLE_LABELS } from "@/lib/types";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { usePaidEnterpriseFeaturesEnabled } from "@/components/settings/usePaidEnterpriseFeaturesEnabled";

const EMAIL_REGEX = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

interface UserInvite {
  email: string;
  role: UserRole;
}

const addUsers = async (url: string, { arg }: { arg: UserInvite[] }) => {
  return await fetch(url, {
    method: "PUT",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify({ users: arg }),
  });
};

interface FormProps {
  onSuccess: () => void;
  onFailure: (res: Response) => void;
}

interface FormValues {
  userInvites: string;
  defaultRole: UserRole;
}

const AddUserFormRenderer = ({
  touched,
  errors,
  isSubmitting,
  handleSubmit,
  values,
  setFieldValue,
}: FormikProps<FormValues>) => {
  const isPaidEnterpriseFeaturesEnabled = usePaidEnterpriseFeaturesEnabled();

  // Get available roles for the dropdown
  const getAvailableRoles = () => {
    return Object.entries(USER_ROLE_LABELS).filter(([role]) => {
      // Don't show external permissioned users because it's scary
      if (role === UserRole.EXT_PERM_USER) return false;

      // Only show limited users if paid enterprise features are enabled
      // Also, don't show these other roles in general
      const isNotVisibleRole =
        (!isPaidEnterpriseFeaturesEnabled &&
          role === UserRole.GLOBAL_CURATOR) ||
        role === UserRole.CURATOR ||
        role === UserRole.LIMITED ||
        role === UserRole.SLACK_USER;

      return !isNotVisibleRole;
    });
  };

  return (
    <Form className="w-full" onSubmit={handleSubmit}>
      <div className="space-y-4">
        <div>
          <label htmlFor="defaultRole" className="block text-sm font-medium mb-2">
            Default Role for All Users
          </label>
          <Select
            value={values.defaultRole}
            onValueChange={(value) => setFieldValue("defaultRole", value)}
          >
            <SelectTrigger>
              <SelectValue placeholder="Select a role" />
            </SelectTrigger>
            <SelectContent>
              {getAvailableRoles().map(([role, label]) => (
                <SelectItem key={role} value={role}>
                  {label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          {touched.defaultRole && errors.defaultRole && (
            <div className="text-error text-sm mt-1">{errors.defaultRole}</div>
          )}
        </div>

        <div>
          <label htmlFor="userInvites" className="block text-sm font-medium mb-2">
            User Invitations (email:role format, one per line)
          </label>
          <Field
            id="userInvites"
            name="userInvites"
            as="textarea"
            className="w-full p-4 min-h-32"
            placeholder="Enter user invitations in format:&#10;<EMAIL>:admin&#10;<EMAIL>:basic&#10;&#10;Or just emails (will use default role):&#10;<EMAIL>&#10;<EMAIL>"
            onKeyDown={(e: React.KeyboardEvent<HTMLTextAreaElement>) => {
              if (e.key === "Enter" && e.ctrlKey) {
                e.preventDefault();
                handleSubmit();
              }
            }}
          />
          {touched.userInvites && errors.userInvites && (
            <div className="text-error text-sm mt-1">{errors.userInvites}</div>
          )}
        </div>
      </div>

      <Button
        className="mx-auto mt-4"
        variant="submit"
        size="sm"
        type="submit"
        disabled={isSubmitting}
      >
        {isSubmitting ? "Adding..." : "Add Users!"}
      </Button>
    </Form>
  );
};

const AddUserForm = withFormik<FormProps, FormValues>({
  mapPropsToValues: (props) => {
    return {
      userInvites: "",
      defaultRole: UserRole.BASIC,
    };
  },
  validate: (values: FormValues): FormikErrors<FormValues> => {
    const errors: FormikErrors<FormValues> = {};

    // Validate default role
    if (!values.defaultRole) {
      errors.defaultRole = "Default role is required";
    }

    // Validate user invites
    if (!values.userInvites.trim()) {
      errors.userInvites = "At least one user invitation is required";
      return errors;
    }

    const lines = values.userInvites.trim().split('\n').filter(line => line.trim());

    for (let line of lines) {
      const trimmedLine = line.trim();
      if (!trimmedLine) continue;

      // Check if line contains email:role format
      if (trimmedLine.includes(':')) {
        const [email, role] = trimmedLine.split(':').map(part => part.trim());

        if (!email || !EMAIL_REGEX.test(email)) {
          errors.userInvites = `Invalid email format: ${email}`;
          break;
        }

        if (!role) {
          errors.userInvites = `Role is required for email: ${email}`;
          break;
        }

        if (!Object.values(UserRole).includes(role as UserRole)) {
          errors.userInvites = `Invalid role "${role}" for email: ${email}. Valid roles: ${Object.values(UserRole).join(', ')}`;
          break;
        }
      } else {
        // Just email format - validate email only
        if (!EMAIL_REGEX.test(trimmedLine)) {
          errors.userInvites = `Invalid email format: ${trimmedLine}`;
          break;
        }
      }
    }

    return errors;
  },
  handleSubmit: async (values: FormValues, formikBag) => {
    const lines = values.userInvites.trim().split('\n').filter(line => line.trim());
    const userInvites: UserInvite[] = [];

    for (let line of lines) {
      const trimmedLine = line.trim();
      if (!trimmedLine) continue;

      if (trimmedLine.includes(':')) {
        // email:role format
        const [email, role] = trimmedLine.split(':').map(part => part.trim());
        userInvites.push({ email, role: role as UserRole });
      } else {
        // just email - use default role
        userInvites.push({ email: trimmedLine, role: values.defaultRole });
      }
    }

    await addUsers("/api/manage/admin/users", { arg: userInvites }).then((res) => {
      if (res.ok) {
        formikBag.props.onSuccess();
      } else {
        formikBag.props.onFailure(res);
      }
    });
  },
})(AddUserFormRenderer);

const BulkAdd = ({ onSuccess, onFailure }: FormProps) => {
  return <AddUserForm onSuccess={onSuccess} onFailure={onFailure} />;
};

export default BulkAdd;
